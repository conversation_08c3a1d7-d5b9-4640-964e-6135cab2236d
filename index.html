<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coffee Shop Map</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        #map {
            height: 100vh;
            width: 100%;
        }
        
        .loading {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            display: none;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            min-width: 250px;
        }

        .search-container {
            margin-bottom: 15px;
        }

        .search-container input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .search-container button {
            width: 100%;
            padding: 8px;
            margin-top: 5px;
            background: #8B4513;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .search-container button:hover {
            background: #A0522D;
        }

        .load-button {
            width: 100%;
            padding: 8px;
            background: #2E8B57;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .load-button:hover {
            background: #3CB371;
        }
        
        .coffee-popup {
            max-width: 300px;
        }
        
        .coffee-popup h3 {
            margin: 0 0 10px 0;
            color: #8B4513;
        }
        
        .coffee-popup p {
            margin: 5px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>☕ Coffee Shop Map</h3>

        <div class="search-container">
            <input type="text" id="locationSearch" placeholder="Enter city or address (e.g., Seattle, WA)" />
            <button id="searchLocation">Search & Load Coffee Shops</button>
        </div>

        <button id="loadCoffeeShops" class="load-button">Load Coffee Shops in Current View</button>

        <p><small>Search for a location or move the map and load coffee shops in the current area</small></p>
    </div>
    
    <div class="loading" id="loading">Loading coffee shops...</div>
    <div id="map"></div>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <script>
        // Initialize the map
        const map = L.map('map').setView([39.8283, -98.5795], 4); // Center of US
        
        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
        
        // Layer group for coffee shop markers
        const coffeeShopsLayer = L.layerGroup().addTo(map);
        
        // Loading indicator
        const loadingDiv = document.getElementById('loading');
        
        // Function to show/hide loading
        function showLoading(show) {
            loadingDiv.style.display = show ? 'block' : 'none';
        }

        // Function to search for a location using Nominatim API
        async function searchLocation(query) {
            try {
                const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=1&addressdetails=1`);
                const data = await response.json();

                if (data.length > 0) {
                    const result = data[0];
                    const lat = parseFloat(result.lat);
                    const lon = parseFloat(result.lon);

                    // Move map to the location
                    map.setView([lat, lon], 13);

                    // Add a temporary marker for the searched location
                    const searchMarker = L.marker([lat, lon], {
                        icon: L.icon({
                            iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-red.png',
                            shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
                            iconSize: [25, 41],
                            iconAnchor: [12, 41],
                            popupAnchor: [1, -34],
                            shadowSize: [41, 41]
                        })
                    }).addTo(map);

                    searchMarker.bindPopup(`<strong>📍 ${result.display_name}</strong><br><small>Searched location</small>`).openPopup();

                    // Remove the search marker after 5 seconds
                    setTimeout(() => {
                        map.removeLayer(searchMarker);
                    }, 5000);

                    return true;
                } else {
                    alert('Location not found. Please try a different search term.');
                    return false;
                }
            } catch (error) {
                console.error('Error searching location:', error);
                alert('Error searching for location. Please try again.');
                return false;
            }
        }
        
        // Function to query Overpass API for coffee shops
        async function loadCoffeeShops() {
            showLoading(true);
            
            // Clear existing markers
            coffeeShopsLayer.clearLayers();
            
            // Get current map bounds
            const bounds = map.getBounds();
            const bbox = `${bounds.getSouth()},${bounds.getWest()},${bounds.getNorth()},${bounds.getEast()}`;
            
            // Overpass query for coffee shops (amenity=cafe)
            const query = `
                [out:json][timeout:25];
                (
                    node["amenity"="cafe"](${bbox});
                    way["amenity"="cafe"](${bbox});
                    relation["amenity"="cafe"](${bbox});
                );
                out center meta;
            `;
            
            try {
                const response = await fetch('https://overpass-api.de/api/interpreter', {
                    method: 'POST',
                    body: query
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Process the results
                data.elements.forEach(element => {
                    let lat, lon;
                    
                    // Get coordinates based on element type
                    if (element.type === 'node') {
                        lat = element.lat;
                        lon = element.lon;
                    } else if (element.center) {
                        lat = element.center.lat;
                        lon = element.center.lon;
                    } else {
                        return; // Skip if no coordinates
                    }
                    
                    // Extract coffee shop information
                    const tags = element.tags || {};
                    const name = tags.name || 'Unnamed Coffee Shop';
                    const address = [
                        tags['addr:housenumber'],
                        tags['addr:street'],
                        tags['addr:city'],
                        tags['addr:state']
                    ].filter(Boolean).join(' ');
                    
                    // Create popup content
                    const popupContent = `
                        <div class="coffee-popup">
                            <h3>☕ ${name}</h3>
                            ${address ? `<p><strong>Address:</strong> ${address}</p>` : ''}
                            ${tags.phone ? `<p><strong>Phone:</strong> ${tags.phone}</p>` : ''}
                            ${tags.website ? `<p><strong>Website:</strong> <a href="${tags.website}" target="_blank">${tags.website}</a></p>` : ''}
                            ${tags.opening_hours ? `<p><strong>Hours:</strong> ${tags.opening_hours}</p>` : ''}
                            ${tags.cuisine ? `<p><strong>Cuisine:</strong> ${tags.cuisine}</p>` : ''}
                            <p><small>Data from OpenStreetMap</small></p>
                        </div>
                    `;
                    
                    // Create marker
                    const marker = L.marker([lat, lon])
                        .bindPopup(popupContent)
                        .addTo(coffeeShopsLayer);
                });
                
                console.log(`Loaded ${data.elements.length} coffee shops`);
                
            } catch (error) {
                console.error('Error loading coffee shops:', error);
                alert('Error loading coffee shops. Please try again.');
            } finally {
                showLoading(false);
            }
        }
        
        // Add click handler to load button
        document.getElementById('loadCoffeeShops').addEventListener('click', loadCoffeeShops);

        // Add click handler to search button
        document.getElementById('searchLocation').addEventListener('click', async () => {
            const query = document.getElementById('locationSearch').value.trim();
            if (query) {
                showLoading(true);
                const found = await searchLocation(query);
                if (found) {
                    // Wait a moment for the map to settle, then load coffee shops
                    setTimeout(() => {
                        loadCoffeeShops();
                    }, 1000);
                } else {
                    showLoading(false);
                }
            } else {
                alert('Please enter a location to search for.');
            }
        });

        // Add enter key support for search input
        document.getElementById('locationSearch').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                document.getElementById('searchLocation').click();
            }
        });

        // Optional: Auto-load when map stops moving (commented out to avoid too many API calls)
        // map.on('moveend', loadCoffeeShops);

        console.log('Coffee Shop Map initialized. Enter a location to search or click "Load Coffee Shops in Current View" to see coffee shops in the current area.');
    </script>
</body>
</html>
