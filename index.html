<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coffee Shop Map</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        #map {
            height: 100vh;
            width: 100%;
        }
        
        .loading {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            display: none;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            min-width: 250px;
        }

        .search-container {
            margin-bottom: 15px;
        }

        .search-container input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .search-container button {
            width: 100%;
            padding: 8px;
            margin-top: 5px;
            background: #8B4513;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .search-container button:hover {
            background: #A0522D;
        }

        .load-button {
            width: 100%;
            padding: 8px;
            background: #2E8B57;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .load-button:hover {
            background: #3CB371;
        }
        
        .coffee-popup {
            max-width: 350px;
        }

        .coffee-popup h3 {
            margin: 0 0 10px 0;
            color: #8B4513;
        }

        .coffee-popup p {
            margin: 5px 0;
            font-size: 14px;
        }

        .rating {
            color: #ff9800;
            font-weight: bold;
        }

        .review-snippet {
            font-style: italic;
            color: #666;
            margin: 8px 0;
            padding: 8px;
            background: #f5f5f5;
            border-radius: 4px;
        }

        .sidebar {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            max-height: 80vh;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            z-index: 1000;
            display: none;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 15px;
            border-bottom: 1px solid #eee;
            background: #8B4513;
            color: white;
            border-radius: 8px 8px 0 0;
        }

        .sidebar-content {
            padding: 15px;
        }

        .coffee-item {
            padding: 12px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .coffee-item:hover {
            background: #f8f8f8;
        }

        .coffee-item:last-child {
            border-bottom: none;
        }

        .coffee-name {
            font-weight: bold;
            color: #8B4513;
            margin-bottom: 4px;
        }

        .coffee-rating {
            color: #ff9800;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .coffee-address {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>☕ Coffee Shop Map</h3>

        <div class="search-container">
            <input type="text" id="locationSearch" placeholder="Enter city or address (e.g., Seattle, WA)" />
            <button id="searchLocation">Search & Load Coffee Shops</button>
        </div>

        <button id="loadCoffeeShops" class="load-button">Load Coffee Shops in Current View</button>

        <p><small>Search for a location or move the map and load coffee shops in the current area</small></p>
    </div>

    <div class="sidebar" id="topCoffeeShops">
        <div class="sidebar-header">
            <h3>☕ Top Rated Coffee Shops</h3>
            <button id="closeSidebar" style="float: right; background: none; border: none; color: white; font-size: 18px; cursor: pointer;">&times;</button>
        </div>
        <div class="sidebar-content" id="sidebarContent">
            <!-- Coffee shops will be populated here -->
        </div>
    </div>

    <div class="loading" id="loading">Loading coffee shops...</div>
    <div id="map"></div>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <script>
        // Google Places API configuration
        const GOOGLE_API_KEY = 'AIzaSyDdbVoKRQ_HI9AEJq1kt1NqgxMt647zxF0';

        // Initialize the map
        const map = L.map('map').setView([39.8283, -98.5795], 4); // Center of US

        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);

        // Layer group for coffee shop markers
        const coffeeShopsLayer = L.layerGroup().addTo(map);

        // Loading indicator
        const loadingDiv = document.getElementById('loading');
        const sidebar = document.getElementById('topCoffeeShops');
        const sidebarContent = document.getElementById('sidebarContent');

        // Store current coffee shops data
        let currentCoffeeShops = [];

        // Function to show/hide loading
        function showLoading(show) {
            loadingDiv.style.display = show ? 'block' : 'none';
        }

        // Function to search for a location using Nominatim API
        async function searchLocation(query) {
            try {
                const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=1&addressdetails=1`);
                const data = await response.json();

                if (data.length > 0) {
                    const result = data[0];
                    const lat = parseFloat(result.lat);
                    const lon = parseFloat(result.lon);

                    // Move map to the location
                    map.setView([lat, lon], 13);

                    // Add a temporary marker for the searched location
                    const searchMarker = L.marker([lat, lon], {
                        icon: L.icon({
                            iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-red.png',
                            shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
                            iconSize: [25, 41],
                            iconAnchor: [12, 41],
                            popupAnchor: [1, -34],
                            shadowSize: [41, 41]
                        })
                    }).addTo(map);

                    searchMarker.bindPopup(`<strong>📍 ${result.display_name}</strong><br><small>Searched location</small>`).openPopup();

                    // Remove the search marker after 5 seconds
                    setTimeout(() => {
                        map.removeLayer(searchMarker);
                    }, 5000);

                    return { lat, lon, name: result.display_name };
                } else {
                    alert('Location not found. Please try a different search term.');
                    return false;
                }
            } catch (error) {
                console.error('Error searching location:', error);
                alert('Error searching for location. Please try again.');
                return false;
            }
        }
        
        // Function to search for coffee shops using Google Places API
        async function loadCoffeeShops(location = null) {
            showLoading(true);

            // Clear existing markers
            coffeeShopsLayer.clearLayers();
            currentCoffeeShops = [];

            try {
                let searchLocation;
                if (location) {
                    searchLocation = `${location.lat},${location.lon}`;
                } else {
                    const center = map.getCenter();
                    searchLocation = `${center.lat},${center.lng}`;
                }

                // Use Google Places Nearby Search API
                const radius = Math.min(50000, Math.max(1000, map.getZoom() * 2000)); // Dynamic radius based on zoom
                const url = `https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=${searchLocation}&radius=${radius}&type=cafe&key=${GOOGLE_API_KEY}`;

                // Note: Direct API calls from browser will be blocked by CORS
                // Using a CORS proxy for demonstration - in production, you'd use your backend
                const proxyUrl = 'https://api.allorigins.win/raw?url=';
                const response = await fetch(proxyUrl + encodeURIComponent(url));

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.status === 'OK') {
                    // Sort by rating (highest first)
                    const sortedPlaces = data.results.sort((a, b) => (b.rating || 0) - (a.rating || 0));
                    currentCoffeeShops = sortedPlaces;

                    // Process each coffee shop
                    for (const place of sortedPlaces) {
                        await createCoffeeShopMarker(place);
                    }

                    // Update sidebar with top rated shops
                    updateSidebar(sortedPlaces.slice(0, 10));

                    console.log(`Loaded ${sortedPlaces.length} coffee shops from Google Places`);
                } else {
                    console.error('Places API error:', data.status);
                    alert('Error loading coffee shops from Google Places API. Please try again.');
                }

            } catch (error) {
                console.error('Error loading coffee shops:', error);
                alert('Error loading coffee shops. Please try again.');
            } finally {
                showLoading(false);
            }
        }
        
        // Function to get place details from Google Places API
        async function getPlaceDetails(placeId) {
            try {
                const url = `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&fields=name,rating,reviews,formatted_phone_number,website,opening_hours,photos&key=${GOOGLE_API_KEY}`;
                const proxyUrl = 'https://api.allorigins.win/raw?url=';
                const response = await fetch(proxyUrl + encodeURIComponent(url));
                const data = await response.json();
                return data.result;
            } catch (error) {
                console.error('Error getting place details:', error);
                return null;
            }
        }

        // Function to create a marker for a coffee shop
        async function createCoffeeShopMarker(place) {
            const lat = place.geometry.location.lat;
            const lng = place.geometry.location.lng;
            const name = place.name;
            const rating = place.rating || 'No rating';
            const priceLevel = place.price_level ? '$'.repeat(place.price_level) : 'Price not available';

            // Create popup content
            let popupContent = `
                <div class="coffee-popup">
                    <h3>☕ ${name}</h3>
                    <p><strong>Rating:</strong> <span class="rating">${rating}⭐</span> (${place.user_ratings_total || 0} reviews)</p>
                    <p><strong>Price Level:</strong> ${priceLevel}</p>
                    <p><strong>Address:</strong> ${place.vicinity}</p>
            `;

            // Add opening hours if available
            if (place.opening_hours) {
                const status = place.opening_hours.open_now ? 'Open now' : 'Closed';
                popupContent += `<p><strong>Status:</strong> ${status}</p>`;
            }

            popupContent += `<p><small>Data from Google Places</small></p></div>`;

            // Create marker
            const marker = L.marker([lat, lng])
                .bindPopup(popupContent)
                .addTo(coffeeShopsLayer);

            // Add click handler to get more details
            marker.on('click', async () => {
                const details = await getPlaceDetails(place.place_id);
                if (details && details.reviews) {
                    const review = details.reviews[0];
                    const reviewSnippet = review ? `
                        <div class="review-snippet">
                            <strong>${review.author_name}:</strong> "${review.text.substring(0, 150)}..."
                            <br><small>Rating: ${review.rating}⭐</small>
                        </div>
                    ` : '';

                    const enhancedPopup = `
                        <div class="coffee-popup">
                            <h3>☕ ${name}</h3>
                            <p><strong>Rating:</strong> <span class="rating">${rating}⭐</span> (${place.user_ratings_total || 0} reviews)</p>
                            <p><strong>Price Level:</strong> ${priceLevel}</p>
                            <p><strong>Address:</strong> ${place.vicinity}</p>
                            ${details.formatted_phone_number ? `<p><strong>Phone:</strong> ${details.formatted_phone_number}</p>` : ''}
                            ${details.website ? `<p><strong>Website:</strong> <a href="${details.website}" target="_blank">Visit Website</a></p>` : ''}
                            ${place.opening_hours ? `<p><strong>Status:</strong> ${place.opening_hours.open_now ? 'Open now' : 'Closed'}</p>` : ''}
                            ${reviewSnippet}
                            <p><small>Data from Google Places</small></p>
                        </div>
                    `;
                    marker.setPopupContent(enhancedPopup);
                }
            });
        }

        // Function to update the sidebar with top coffee shops
        function updateSidebar(coffeeShops) {
            sidebarContent.innerHTML = '';

            coffeeShops.forEach((shop, index) => {
                const shopElement = document.createElement('div');
                shopElement.className = 'coffee-item';
                shopElement.innerHTML = `
                    <div class="coffee-name">${index + 1}. ${shop.name}</div>
                    <div class="coffee-rating">⭐ ${shop.rating || 'No rating'} (${shop.user_ratings_total || 0} reviews)</div>
                    <div class="coffee-address">${shop.vicinity}</div>
                `;

                // Add click handler to center map on this coffee shop
                shopElement.addEventListener('click', () => {
                    const lat = shop.geometry.location.lat;
                    const lng = shop.geometry.location.lng;
                    map.setView([lat, lng], 16);

                    // Find and open the corresponding marker popup
                    coffeeShopsLayer.eachLayer(layer => {
                        if (layer.getLatLng().lat === lat && layer.getLatLng().lng === lng) {
                            layer.openPopup();
                        }
                    });
                });

                sidebarContent.appendChild(shopElement);
            });

            // Show sidebar
            sidebar.style.display = 'block';
        }

        // Add click handler to load button
        document.getElementById('loadCoffeeShops').addEventListener('click', () => loadCoffeeShops());

        // Add click handler to search button
        document.getElementById('searchLocation').addEventListener('click', async () => {
            const query = document.getElementById('locationSearch').value.trim();
            if (query) {
                showLoading(true);
                const location = await searchLocation(query);
                if (location) {
                    // Wait a moment for the map to settle, then load coffee shops
                    setTimeout(() => {
                        loadCoffeeShops(location);
                    }, 1000);
                } else {
                    showLoading(false);
                }
            } else {
                alert('Please enter a location to search for.');
            }
        });

        // Add enter key support for search input
        document.getElementById('locationSearch').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                document.getElementById('searchLocation').click();
            }
        });

        // Add sidebar close functionality
        document.getElementById('closeSidebar').addEventListener('click', () => {
            sidebar.style.display = 'none';
        });

        // Optional: Auto-load when map stops moving (commented out to avoid too many API calls)
        // map.on('moveend', () => loadCoffeeShops());

        console.log('Coffee Shop Map initialized with Google Places API. Enter a location to search or click "Load Coffee Shops in Current View" to see coffee shops with ratings and reviews!');
    </script>
</body>
</html>
