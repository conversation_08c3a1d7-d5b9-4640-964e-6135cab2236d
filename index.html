<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coffee Shop Map</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        #map {
            height: 100vh;
            width: 100%;
        }
        
        .loading {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            display: none;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        
        .coffee-popup {
            max-width: 300px;
        }
        
        .coffee-popup h3 {
            margin: 0 0 10px 0;
            color: #8B4513;
        }
        
        .coffee-popup p {
            margin: 5px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h3>☕ Coffee Shop Map</h3>
        <button id="loadCoffeeShops">Load Coffee Shops in View</button>
        <p><small>Move the map and click the button to load coffee shops in the current area</small></p>
    </div>
    
    <div class="loading" id="loading">Loading coffee shops...</div>
    <div id="map"></div>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <script>
        // Initialize the map
        const map = L.map('map').setView([39.8283, -98.5795], 4); // Center of US
        
        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
        
        // Layer group for coffee shop markers
        const coffeeShopsLayer = L.layerGroup().addTo(map);
        
        // Loading indicator
        const loadingDiv = document.getElementById('loading');
        
        // Function to show/hide loading
        function showLoading(show) {
            loadingDiv.style.display = show ? 'block' : 'none';
        }
        
        // Function to query Overpass API for coffee shops
        async function loadCoffeeShops() {
            showLoading(true);
            
            // Clear existing markers
            coffeeShopsLayer.clearLayers();
            
            // Get current map bounds
            const bounds = map.getBounds();
            const bbox = `${bounds.getSouth()},${bounds.getWest()},${bounds.getNorth()},${bounds.getEast()}`;
            
            // Overpass query for coffee shops (amenity=cafe)
            const query = `
                [out:json][timeout:25];
                (
                    node["amenity"="cafe"](${bbox});
                    way["amenity"="cafe"](${bbox});
                    relation["amenity"="cafe"](${bbox});
                );
                out center meta;
            `;
            
            try {
                const response = await fetch('https://overpass-api.de/api/interpreter', {
                    method: 'POST',
                    body: query
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Process the results
                data.elements.forEach(element => {
                    let lat, lon;
                    
                    // Get coordinates based on element type
                    if (element.type === 'node') {
                        lat = element.lat;
                        lon = element.lon;
                    } else if (element.center) {
                        lat = element.center.lat;
                        lon = element.center.lon;
                    } else {
                        return; // Skip if no coordinates
                    }
                    
                    // Extract coffee shop information
                    const tags = element.tags || {};
                    const name = tags.name || 'Unnamed Coffee Shop';
                    const address = [
                        tags['addr:housenumber'],
                        tags['addr:street'],
                        tags['addr:city'],
                        tags['addr:state']
                    ].filter(Boolean).join(' ');
                    
                    // Create popup content
                    const popupContent = `
                        <div class="coffee-popup">
                            <h3>☕ ${name}</h3>
                            ${address ? `<p><strong>Address:</strong> ${address}</p>` : ''}
                            ${tags.phone ? `<p><strong>Phone:</strong> ${tags.phone}</p>` : ''}
                            ${tags.website ? `<p><strong>Website:</strong> <a href="${tags.website}" target="_blank">${tags.website}</a></p>` : ''}
                            ${tags.opening_hours ? `<p><strong>Hours:</strong> ${tags.opening_hours}</p>` : ''}
                            ${tags.cuisine ? `<p><strong>Cuisine:</strong> ${tags.cuisine}</p>` : ''}
                            <p><small>Data from OpenStreetMap</small></p>
                        </div>
                    `;
                    
                    // Create marker
                    const marker = L.marker([lat, lon])
                        .bindPopup(popupContent)
                        .addTo(coffeeShopsLayer);
                });
                
                console.log(`Loaded ${data.elements.length} coffee shops`);
                
            } catch (error) {
                console.error('Error loading coffee shops:', error);
                alert('Error loading coffee shops. Please try again.');
            } finally {
                showLoading(false);
            }
        }
        
        // Add click handler to load button
        document.getElementById('loadCoffeeShops').addEventListener('click', loadCoffeeShops);
        
        // Optional: Auto-load when map stops moving (commented out to avoid too many API calls)
        // map.on('moveend', loadCoffeeShops);
        
        console.log('Coffee Shop Map initialized. Click "Load Coffee Shops in View" to see coffee shops in the current area.');
    </script>
</body>
</html>
