<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coffee Shop Map</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        #map {
            height: 100vh;
            width: 100%;
        }
        
        .loading {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            display: none;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            min-width: 250px;
            transition: transform 0.3s ease;
        }

        .controls.hidden {
            transform: translateX(-100%);
        }

        .toggle-controls {
            position: fixed;
            top: 15px;
            left: 15px;
            background: #8B4513;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            z-index: 1001;
            font-size: 18px;
            display: none;
        }

        .toggle-controls:hover {
            background: #A0522D;
        }

        .search-container {
            margin-bottom: 15px;
        }

        .search-container input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .search-container button {
            width: 100%;
            padding: 8px;
            margin-top: 5px;
            background: #8B4513;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .search-container button:hover {
            background: #A0522D;
        }

        .load-button {
            width: 100%;
            padding: 8px;
            background: #2E8B57;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .load-button:hover {
            background: #3CB371;
        }
        
        .coffee-popup {
            max-width: 350px;
        }

        .coffee-popup h3 {
            margin: 0 0 10px 0;
            color: #8B4513;
        }

        .coffee-popup p {
            margin: 5px 0;
            font-size: 14px;
        }

        .rating {
            color: #ff9800;
            font-weight: bold;
        }

        .review-snippet {
            font-style: italic;
            color: #666;
            margin: 8px 0;
            padding: 8px;
            background: #f5f5f5;
            border-radius: 4px;
        }

        .sidebar {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            max-height: 80vh;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            z-index: 1000;
            display: none;
            overflow-y: auto;
            transition: transform 0.3s ease;
        }

        .sidebar.hidden {
            transform: translateX(100%);
        }

        .toggle-sidebar {
            position: fixed;
            top: 15px;
            right: 15px;
            background: #8B4513;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            z-index: 1001;
            font-size: 18px;
            display: none;
        }

        .toggle-sidebar:hover {
            background: #A0522D;
        }

        .sidebar-header {
            padding: 15px;
            border-bottom: 1px solid #eee;
            background: #8B4513;
            color: white;
            border-radius: 8px 8px 0 0;
        }

        .sidebar-content {
            padding: 15px;
        }

        .coffee-item {
            padding: 12px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .coffee-item:hover {
            background: #f8f8f8;
        }

        .coffee-item:last-child {
            border-bottom: none;
        }

        .coffee-name {
            font-weight: bold;
            color: #8B4513;
            margin-bottom: 4px;
        }

        .coffee-rating {
            color: #ff9800;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .coffee-address {
            font-size: 12px;
            color: #666;
        }

        .coffee-details {
            display: none;
            margin-top: 10px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
            border-left: 3px solid #8B4513;
        }

        .coffee-details.expanded {
            display: block;
        }

        .detail-section {
            margin-bottom: 10px;
        }

        .detail-section h4 {
            margin: 0 0 5px 0;
            color: #8B4513;
            font-size: 14px;
        }

        .hours-list {
            font-size: 12px;
            line-height: 1.4;
        }

        .review-item {
            background: white;
            padding: 8px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 2px solid #ff9800;
        }

        .review-author {
            font-weight: bold;
            font-size: 12px;
            color: #8B4513;
        }

        .review-text {
            font-size: 12px;
            margin: 4px 0;
            line-height: 1.3;
        }

        .review-rating {
            font-size: 11px;
            color: #ff9800;
        }

        .loading-details {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 10px;
        }
    </style>
</head>
<body>
    <button class="toggle-controls" id="toggleControls">☰</button>

    <div class="controls" id="controls">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <h3 style="margin: 0;">☕ Coffee Shop Map</h3>
            <button id="hideControls" style="background: none; border: none; font-size: 18px; cursor: pointer; color: #8B4513;">&times;</button>
        </div>

        <div class="search-container">
            <input type="text" id="locationSearch" placeholder="Enter city or address (e.g., Seattle, WA)" />
            <button id="searchLocation">Search & Load Coffee Shops</button>
        </div>

        <button id="loadCoffeeShops" class="load-button">Load Coffee Shops in Current View</button>

        <p><small>Search for a location or move the map and load coffee shops in the current area</small></p>
    </div>

    <button class="toggle-sidebar" id="toggleSidebar">⭐</button>

    <div class="sidebar" id="topCoffeeShops">
        <div class="sidebar-header">
            <h3>☕ Top Rated Coffee Shops</h3>
            <button id="closeSidebar" style="float: right; background: none; border: none; color: white; font-size: 18px; cursor: pointer;">&times;</button>
        </div>
        <div class="sidebar-content" id="sidebarContent">
            <!-- Coffee shops will be populated here -->
        </div>
    </div>

    <div class="loading" id="loading">Loading coffee shops...</div>
    <div id="map"></div>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <script>
        // Google Places API configuration
        const GOOGLE_API_KEY = 'AIzaSyDdbVoKRQ_HI9AEJq1kt1NqgxMt647zxF0';

        // Initialize the map
        const map = L.map('map').setView([39.8283, -98.5795], 4); // Center of US

        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);

        // Layer group for coffee shop markers
        const coffeeShopsLayer = L.layerGroup().addTo(map);

        // Loading indicator
        const loadingDiv = document.getElementById('loading');
        const sidebar = document.getElementById('topCoffeeShops');
        const sidebarContent = document.getElementById('sidebarContent');
        const controls = document.getElementById('controls');
        const toggleControlsBtn = document.getElementById('toggleControls');
        const toggleSidebarBtn = document.getElementById('toggleSidebar');

        // Store current coffee shops data
        let currentCoffeeShops = [];
        let controlsVisible = true;
        let sidebarVisible = false;

        // Function to show/hide loading
        function showLoading(show) {
            loadingDiv.style.display = show ? 'block' : 'none';
        }

        // Function to search for a location using Nominatim API
        async function searchLocation(query) {
            try {
                const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=1&addressdetails=1`);
                const data = await response.json();

                if (data.length > 0) {
                    const result = data[0];
                    const lat = parseFloat(result.lat);
                    const lon = parseFloat(result.lon);

                    // Move map to the location
                    map.setView([lat, lon], 13);

                    // Add a temporary marker for the searched location
                    const searchMarker = L.marker([lat, lon], {
                        icon: L.icon({
                            iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-red.png',
                            shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
                            iconSize: [25, 41],
                            iconAnchor: [12, 41],
                            popupAnchor: [1, -34],
                            shadowSize: [41, 41]
                        })
                    }).addTo(map);

                    searchMarker.bindPopup(`<strong>📍 ${result.display_name}</strong><br><small>Searched location</small>`).openPopup();

                    // Remove the search marker after 5 seconds
                    setTimeout(() => {
                        map.removeLayer(searchMarker);
                    }, 5000);

                    return { lat, lon, name: result.display_name };
                } else {
                    alert('Location not found. Please try a different search term.');
                    return false;
                }
            } catch (error) {
                console.error('Error searching location:', error);
                alert('Error searching for location. Please try again.');
                return false;
            }
        }
        
        // Function to search for coffee shops using Google Places API
        // NOTE: This implementation uses the legacy API with CORS proxy for demo purposes.
        // For production, you should:
        // 1. Use the new Places API (v1) with proper POST requests from your backend
        // 2. Implement proper error handling and rate limiting
        // 3. Use field masks to optimize billing (places.displayName,places.rating,etc.)
        // 4. Consider caching results to reduce API calls
        async function loadCoffeeShops(location = null) {
            showLoading(true);

            // Clear existing markers
            coffeeShopsLayer.clearLayers();
            currentCoffeeShops = [];

            try {
                let center;
                if (location) {
                    center = { latitude: location.lat, longitude: location.lon };
                } else {
                    const mapCenter = map.getCenter();
                    center = { latitude: mapCenter.lat, longitude: mapCenter.lng };
                }

                // Use the new Places API with proper POST request
                const radius = Math.min(25000, Math.max(2000, map.getZoom() * 1500));

                // Primary search for cafes using the new API format
                const requestBody = {
                    includedTypes: ["cafe"],
                    maxResultCount: 20,
                    locationRestriction: {
                        circle: {
                            center: center,
                            radius: radius
                        }
                    },
                    rankPreference: "POPULARITY"
                };

                // For demo purposes, we'll use the legacy API with CORS proxy
                // In production, you should use your backend to make these calls
                const proxyUrl = 'https://api.allorigins.win/raw?url=';
                const allPlaces = new Map(); // Use Map to avoid duplicates by place_id

                // Multiple searches for comprehensive results
                const searches = [
                    // Primary: Search for cafes
                    `https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=${center.latitude},${center.longitude}&radius=${radius}&type=cafe&key=${GOOGLE_API_KEY}`,
                    // Secondary: Text search for coffee shops
                    `https://maps.googleapis.com/maps/api/place/textsearch/json?query=coffee+shop&location=${center.latitude},${center.longitude}&radius=${radius}&key=${GOOGLE_API_KEY}`
                ];

                // Execute searches
                for (const searchUrl of searches) {
                    try {
                        const response = await fetch(proxyUrl + encodeURIComponent(searchUrl));
                        if (response.ok) {
                            const data = await response.json();
                            if (data.status === 'OK' && data.results) {
                                data.results.forEach(place => {
                                    const name = place.name || '';
                                    const types = place.types || [];

                                    if (isCoffeeRelated(name, types)) {
                                        allPlaces.set(place.place_id, place);
                                    }
                                });
                            }
                        }
                    } catch (error) {
                        console.warn('Search failed:', error);
                    }
                }

                if (allPlaces.size > 0) {
                    // Convert to array and sort by rating
                    const coffeeShops = Array.from(allPlaces.values())
                        .sort((a, b) => (b.rating || 0) - (a.rating || 0));

                    currentCoffeeShops = coffeeShops;

                    // Process each coffee shop
                    for (const place of coffeeShops) {
                        await createCoffeeShopMarker(place);
                    }

                    // Update sidebar with top rated shops
                    updateSidebar(coffeeShops.slice(0, 15));

                    console.log(`Loaded ${coffeeShops.length} coffee shops from Google Places`);
                } else {
                    alert('No coffee shops found in this area. Try a different location or zoom level.');
                }

            } catch (error) {
                console.error('Error loading coffee shops:', error);
                alert('Error loading coffee shops. Please try again.');
            } finally {
                showLoading(false);
            }
        }

        // Function to determine if a place is coffee-related (improved filtering)
        function isCoffeeRelated(name, types) {
            const coffeeKeywords = [
                'coffee', 'cafe', 'espresso', 'latte', 'cappuccino', 'roaster', 'roastery',
                'brew', 'bean', 'grind', 'barista', 'americano', 'macchiato', 'mocha',
                'starbucks', 'dunkin', 'peet', 'blue bottle', 'intelligentsia',
                'counter culture', 'ritual', 'philz', 'caribou', 'tim hortons',
                'costa', 'nero', 'lavazza', 'illy'
            ];

            // Google Places API types that indicate coffee shops
            const coffeeTypes = ['cafe', 'coffee_shop', 'bakery'];

            // Check if name contains coffee keywords
            const nameMatch = coffeeKeywords.some(keyword =>
                name.toLowerCase().includes(keyword.toLowerCase())
            );

            // Check if types include coffee-related types
            const typeMatch = types.some(type => coffeeTypes.includes(type));

            // Strong exclusions - these are definitely not coffee shops
            const strongExclusions = [
                'gas_station', 'convenience_store', 'supermarket', 'grocery_or_supermarket',
                'night_club', 'bar', 'liquor_store', 'pharmacy', 'hospital', 'bank',
                'atm', 'car_dealer', 'car_repair', 'clothing_store', 'electronics_store'
            ];

            // Weak exclusions - only exclude if no coffee keywords in name
            const weakExclusions = ['restaurant', 'meal_takeaway', 'food'];

            const hasStrongExclusion = types.some(type => strongExclusions.includes(type));
            const hasWeakExclusion = types.some(type => weakExclusions.includes(type));

            // If it has strong exclusions, definitely not a coffee shop
            if (hasStrongExclusion) {
                return false;
            }

            // If it has weak exclusions, only include if name clearly indicates coffee
            if (hasWeakExclusion && !nameMatch) {
                return false;
            }

            // Include if it matches coffee types or has coffee keywords
            return typeMatch || nameMatch;
        }
        
        // Function to get place details from Google Places API
        async function getPlaceDetails(placeId) {
            try {
                const url = `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&fields=name,rating,reviews,formatted_phone_number,website,opening_hours,photos&key=${GOOGLE_API_KEY}`;
                const proxyUrl = 'https://api.allorigins.win/raw?url=';
                const response = await fetch(proxyUrl + encodeURIComponent(url));
                const data = await response.json();
                return data.result;
            } catch (error) {
                console.error('Error getting place details:', error);
                return null;
            }
        }

        // Function to create a marker for a coffee shop
        async function createCoffeeShopMarker(place) {
            const lat = place.geometry.location.lat;
            const lng = place.geometry.location.lng;
            const name = place.name;
            const rating = place.rating || 'No rating';
            const priceLevel = place.price_level ? '$'.repeat(place.price_level) : 'Price not available';

            // Create popup content
            let popupContent = `
                <div class="coffee-popup">
                    <h3>☕ ${name}</h3>
                    <p><strong>Rating:</strong> <span class="rating">${rating}⭐</span> (${place.user_ratings_total || 0} reviews)</p>
                    <p><strong>Price Level:</strong> ${priceLevel}</p>
                    <p><strong>Address:</strong> ${place.vicinity}</p>
            `;

            // Add opening hours if available
            if (place.opening_hours) {
                const status = place.opening_hours.open_now ? 'Open now' : 'Closed';
                popupContent += `<p><strong>Status:</strong> ${status}</p>`;
            }

            popupContent += `<p><small>Data from Google Places</small></p></div>`;

            // Create marker
            const marker = L.marker([lat, lng])
                .bindPopup(popupContent)
                .addTo(coffeeShopsLayer);

            // Add click handler to get more details
            marker.on('click', async () => {
                const details = await getPlaceDetails(place.place_id);
                if (details && details.reviews) {
                    const review = details.reviews[0];
                    const reviewSnippet = review ? `
                        <div class="review-snippet">
                            <strong>${review.author_name}:</strong> "${review.text.substring(0, 150)}..."
                            <br><small>Rating: ${review.rating}⭐</small>
                        </div>
                    ` : '';

                    const enhancedPopup = `
                        <div class="coffee-popup">
                            <h3>☕ ${name}</h3>
                            <p><strong>Rating:</strong> <span class="rating">${rating}⭐</span> (${place.user_ratings_total || 0} reviews)</p>
                            <p><strong>Price Level:</strong> ${priceLevel}</p>
                            <p><strong>Address:</strong> ${place.vicinity}</p>
                            ${details.formatted_phone_number ? `<p><strong>Phone:</strong> ${details.formatted_phone_number}</p>` : ''}
                            ${details.website ? `<p><strong>Website:</strong> <a href="${details.website}" target="_blank">Visit Website</a></p>` : ''}
                            ${place.opening_hours ? `<p><strong>Status:</strong> ${place.opening_hours.open_now ? 'Open now' : 'Closed'}</p>` : ''}
                            ${reviewSnippet}
                            <p><small>Data from Google Places</small></p>
                        </div>
                    `;
                    marker.setPopupContent(enhancedPopup);
                }
            });
        }

        // Function to update the sidebar with top coffee shops
        function updateSidebar(coffeeShops) {
            sidebarContent.innerHTML = '';

            coffeeShops.forEach((shop, index) => {
                const shopElement = document.createElement('div');
                shopElement.className = 'coffee-item';

                const detailsId = `details-${shop.place_id}`;

                shopElement.innerHTML = `
                    <div class="coffee-name">${index + 1}. ${shop.name}</div>
                    <div class="coffee-rating">⭐ ${shop.rating || 'No rating'} (${shop.user_ratings_total || 0} reviews)</div>
                    <div class="coffee-address">${shop.vicinity || shop.formatted_address || 'Address not available'}</div>
                    <div class="coffee-details" id="${detailsId}">
                        <div class="loading-details">Loading details...</div>
                    </div>
                `;

                // Add click handler to show details and center map
                shopElement.addEventListener('click', async () => {
                    const detailsDiv = document.getElementById(detailsId);
                    const isExpanded = detailsDiv.classList.contains('expanded');

                    // Close all other expanded details
                    document.querySelectorAll('.coffee-details.expanded').forEach(el => {
                        el.classList.remove('expanded');
                    });

                    if (!isExpanded) {
                        // Show details
                        detailsDiv.classList.add('expanded');

                        // Center map on this coffee shop
                        const lat = shop.geometry.location.lat;
                        const lng = shop.geometry.location.lng;
                        map.setView([lat, lng], 16);

                        // Find and open the corresponding marker popup
                        coffeeShopsLayer.eachLayer(layer => {
                            if (Math.abs(layer.getLatLng().lat - lat) < 0.0001 &&
                                Math.abs(layer.getLatLng().lng - lng) < 0.0001) {
                                layer.openPopup();
                            }
                        });

                        // Load detailed information
                        await loadShopDetails(shop, detailsId);
                    }
                });

                sidebarContent.appendChild(shopElement);
            });

            // Show sidebar and toggle button
            sidebar.style.display = 'block';
            toggleSidebarBtn.style.display = 'block';
            sidebarVisible = true;
        }

        // Function to load detailed shop information
        async function loadShopDetails(shop, detailsId) {
            const detailsDiv = document.getElementById(detailsId);

            try {
                const details = await getPlaceDetails(shop.place_id);

                if (details) {
                    let detailsHTML = '';

                    // Opening hours
                    if (details.opening_hours) {
                        detailsHTML += `
                            <div class="detail-section">
                                <h4>🕒 Hours</h4>
                                <div class="hours-list">
                                    ${details.opening_hours.weekday_text ?
                                        details.opening_hours.weekday_text.map(day => `<div>${day}</div>`).join('') :
                                        `<div>Currently ${details.opening_hours.open_now ? 'Open' : 'Closed'}</div>`
                                    }
                                </div>
                            </div>
                        `;
                    }

                    // Contact information
                    if (details.formatted_phone_number || details.website) {
                        detailsHTML += `<div class="detail-section"><h4>📞 Contact</h4>`;
                        if (details.formatted_phone_number) {
                            detailsHTML += `<div>📱 ${details.formatted_phone_number}</div>`;
                        }
                        if (details.website) {
                            detailsHTML += `<div>🌐 <a href="${details.website}" target="_blank">Website</a></div>`;
                        }
                        detailsHTML += `</div>`;
                    }

                    // Reviews
                    if (details.reviews && details.reviews.length > 0) {
                        detailsHTML += `
                            <div class="detail-section">
                                <h4>💬 Recent Reviews</h4>
                                ${details.reviews.slice(0, 3).map(review => `
                                    <div class="review-item">
                                        <div class="review-author">${review.author_name}</div>
                                        <div class="review-rating">${'⭐'.repeat(review.rating)} ${review.rating}/5</div>
                                        <div class="review-text">${review.text.length > 150 ?
                                            review.text.substring(0, 150) + '...' :
                                            review.text}</div>
                                    </div>
                                `).join('')}
                            </div>
                        `;
                    }

                    // Google Maps link
                    detailsHTML += `
                        <div class="detail-section">
                            <a href="https://www.google.com/maps/place/?q=place_id:${shop.place_id}" target="_blank"
                               style="color: #8B4513; text-decoration: none; font-weight: bold;">
                                🗺️ View on Google Maps
                            </a>
                        </div>
                    `;

                    detailsDiv.innerHTML = detailsHTML;
                } else {
                    detailsDiv.innerHTML = '<div class="loading-details">Details not available</div>';
                }
            } catch (error) {
                console.error('Error loading shop details:', error);
                detailsDiv.innerHTML = '<div class="loading-details">Error loading details</div>';
            }
        }

        // Function to toggle controls visibility
        function toggleControls() {
            controlsVisible = !controlsVisible;
            if (controlsVisible) {
                controls.classList.remove('hidden');
                toggleControlsBtn.style.display = 'none';
            } else {
                controls.classList.add('hidden');
                toggleControlsBtn.style.display = 'block';
            }
        }

        // Function to toggle sidebar visibility
        function toggleSidebar() {
            if (sidebarVisible) {
                sidebar.style.display = 'none';
                toggleSidebarBtn.style.display = 'block';
                sidebarVisible = false;
            } else {
                sidebar.style.display = 'block';
                toggleSidebarBtn.style.display = 'block';
                sidebarVisible = true;
            }
        }

        // Add click handler to load button
        document.getElementById('loadCoffeeShops').addEventListener('click', () => loadCoffeeShops());

        // Add click handler to search button
        document.getElementById('searchLocation').addEventListener('click', async () => {
            const query = document.getElementById('locationSearch').value.trim();
            if (query) {
                showLoading(true);
                const location = await searchLocation(query);
                if (location) {
                    // Wait a moment for the map to settle, then load coffee shops
                    setTimeout(() => {
                        loadCoffeeShops(location);
                    }, 1000);
                } else {
                    showLoading(false);
                }
            } else {
                alert('Please enter a location to search for.');
            }
        });

        // Add enter key support for search input
        document.getElementById('locationSearch').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                document.getElementById('searchLocation').click();
            }
        });

        // Add sidebar close functionality
        document.getElementById('closeSidebar').addEventListener('click', () => {
            sidebar.style.display = 'none';
            sidebarVisible = false;
        });

        // Add controls hide functionality
        document.getElementById('hideControls').addEventListener('click', toggleControls);

        // Add toggle button functionality
        toggleControlsBtn.addEventListener('click', toggleControls);
        toggleSidebarBtn.addEventListener('click', toggleSidebar);

        // Optional: Auto-load when map stops moving (commented out to avoid too many API calls)
        // map.on('moveend', () => loadCoffeeShops());

        console.log('Coffee Shop Map initialized with Google Places API. Enter a location to search or click "Load Coffee Shops in Current View" to see coffee shops with ratings and reviews!');
    </script>
</body>
</html>
